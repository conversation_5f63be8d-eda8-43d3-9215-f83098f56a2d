/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable strict mode to prevent double-mounting in development
  reactStrictMode: false,
  
  // Optimize webpack configuration
  webpack: (config, { dev, isServer }) => {
    // Handle file extensions
    config.resolve.extensions = ['.js', '.jsx', '.ts', '.tsx', ...config.resolve.extensions];
    
    // Optimize build in development
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
      
      // Reduce bundle size in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }
    
    return config;
  },
  
  // Experimental features that might help with stability
  experimental: {
    optimizePackageImports: ['lucide-react', '@tabler/icons-react'],
  },
  
  // Handle CSS and static assets better
  swcMinify: true,
  
  // Reduce memory usage
  outputFileTracing: false,
};

export default nextConfig;