import { NextResponse } from 'next/server';

export function middleware(request) {
  const url = request.nextUrl.clone();
  const role = request.cookies.get('role')?.value;

  //Debug log (shows in terminal)
  console.log('[Middleware Triggered]');
  console.log('URL:', url.pathname);
  console.log('Role:', role);

  // For testing purposes, you can uncomment the following:
  // const role = 'ADMIN'; // or 'STUDENT'

  // Redirect profile URLs based on role
  if (url.pathname === '/profile' && role === 'ADMIN') {
    url.pathname = '/admin/profile';
    return NextResponse.redirect(url);
  }

  // Handle settings redirects based on role
  if (url.pathname === '/settings' && role === 'ADMIN') {
    url.pathname = '/admin/settings';
    return NextResponse.redirect(url);
  }

  // Block /profile unless logged in as student or admin
  if (url.pathname.startsWith('/profile') && role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Admin-only access
  if (url.pathname.startsWith('/admin') && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Block student pages unless logged in as student or admin
  if ((url.pathname.startsWith('/myjobs') || 
       url.pathname.startsWith('/explore') || 
       url.pathname.startsWith('/jobpostings') || 
       url.pathname.startsWith('/companies') || 
       url.pathname.startsWith('/company/') || 
       url.pathname.startsWith('/events') || 
       url.pathname.startsWith('/inbox')) && 
      role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Block homepage `/` unless student or admin
  if (url.pathname === '/' && role !== 'STUDENT' && role !== 'ADMIN') {
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // Otherwise allow the request
  return NextResponse.next();
}

// 🔁 Apply middleware to selected routes
export const config = {
  matcher: [
    '/',
    '/myjobs',
    '/explore',
    '/jobpostings',
    '/companies',
    '/company/:path*',
    '/events',
    '/inbox',
    '/profile/:path*',
    '/admin/:path*',
    '/settings'  // Add settings to middleware matcher
  ]
};
